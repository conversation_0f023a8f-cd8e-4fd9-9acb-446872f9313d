const React = require('react');
const { useState, useEffect } = React;
const { useHistory, useLocation } = require('react-router-dom');

// Component để demo cách sử dụng params thay vì routes
const ParamBasedNavigation = () => {
  const history = useHistory();
  const location = useLocation();
  const [currentPage, setCurrentPage] = useState('dashboard');

  // Lấy page từ URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const page = urlParams.get('page') || 'dashboard';
    setCurrentPage(page);
  }, [location.search]);

  // Hàm để thay đổi page bằng cách update URL params
  const navigateToPage = (page) => {
    const urlParams = new URLSearchParams(location.search);
    urlParams.set('page', page);
    
    // <PERSON><PERSON> thể thêm sub-page nếu cần
    if (page.includes('/')) {
      const [mainPage, subPage] = page.split('/');
      urlParams.set('page', mainPage);
      urlParams.set('subPage', subPage);
    }
    
    // Update URL với params mới
    history.push(`${location.pathname}?${urlParams.toString()}`);
  };

  // Render content dựa trên currentPage
  const renderPageContent = () => {
    const urlParams = new URLSearchParams(location.search);
    const page = urlParams.get('page') || 'dashboard';
    const subPage = urlParams.get('subPage');

    switch (page) {
      case 'dashboard':
        return React.createElement('div', null, 'Dashboard Content');
      
      case 'orders':
        return React.createElement('div', null, 'Orders Management Content');
      
      case 'news':
        switch (subPage) {
          case 'articles':
            return React.createElement('div', null, 'News Articles Content');
          case 'categories':
            return React.createElement('div', null, 'News Categories Content');
          default:
            return React.createElement('div', null, 'News Overview Content');
        }
      
      case 'products':
        switch (subPage) {
          case 'list':
            return React.createElement('div', null, 'Products List Content');
          case 'categories':
            return React.createElement('div', null, 'Product Categories Content');
          case 'brands':
            return React.createElement('div', null, 'Product Brands Content');
          case 'promotions':
            return React.createElement('div', null, 'Product Promotions Content');
          default:
            return React.createElement('div', null, 'Products Overview Content');
        }
      
      case 'users':
        switch (subPage) {
          case 'list':
            return React.createElement('div', null, 'Users List Content');
          case 'commissions':
            return React.createElement('div', null, 'Commissions Content');
          default:
            return React.createElement('div', null, 'Users Overview Content');
        }
      
      case 'affiliate':
        switch (subPage) {
          case 'overview':
            return React.createElement('div', null, 'Affiliate Overview Content');
          case 'withdrawals':
            return React.createElement('div', null, 'Affiliate Withdrawals Content');
          default:
            return React.createElement('div', null, 'Affiliate Content');
        }
      
      case 'settings':
        return React.createElement('div', null, 'Settings Content');
      
      default:
        return React.createElement('div', null, 'Page Not Found');
    }
  };

  // Menu items cho navigation
  const menuItems = [
    { id: 'dashboard', title: 'Dashboard', param: 'dashboard' },
    { id: 'orders', title: 'Orders', param: 'orders' },
    { 
      id: 'news', 
      title: 'News', 
      children: [
        { title: 'Articles', param: 'news', subParam: 'articles' },
        { title: 'Categories', param: 'news', subParam: 'categories' }
      ]
    },
    { 
      id: 'products', 
      title: 'Products', 
      children: [
        { title: 'List', param: 'products', subParam: 'list' },
        { title: 'Categories', param: 'products', subParam: 'categories' },
        { title: 'Brands', param: 'products', subParam: 'brands' },
        { title: 'Promotions', param: 'products', subParam: 'promotions' }
      ]
    },
    { 
      id: 'users', 
      title: 'Users', 
      children: [
        { title: 'List', param: 'users', subParam: 'list' },
        { title: 'Commissions', param: 'users', subParam: 'commissions' }
      ]
    },
    { 
      id: 'affiliate', 
      title: 'Affiliate', 
      children: [
        { title: 'Overview', param: 'affiliate', subParam: 'overview' },
        { title: 'Withdrawals', param: 'affiliate', subParam: 'withdrawals' }
      ]
    },
    { id: 'settings', title: 'Settings', param: 'settings' }
  ];

  // Hàm để handle click menu
  const handleMenuClick = (param, subParam = null) => {
    const urlParams = new URLSearchParams();
    urlParams.set('page', param);
    if (subParam) {
      urlParams.set('subPage', subParam);
    }
    history.push(`${location.pathname}?${urlParams.toString()}`);
  };

  return React.createElement(
    'div',
    { style: { display: 'flex', minHeight: '100vh' } },
    [
      // Sidebar với navigation
      React.createElement(
        'div',
        { 
          key: 'sidebar',
          style: { 
            width: '250px', 
            backgroundColor: '#f5f5f5', 
            padding: '20px',
            borderRight: '1px solid #ddd'
          } 
        },
        [
          React.createElement('h3', { key: 'title' }, 'Navigation (Param-based)'),
          React.createElement(
            'ul',
            { key: 'menu', style: { listStyle: 'none', padding: 0 } },
            menuItems.map((item) =>
              React.createElement(
                'li',
                { key: item.id, style: { marginBottom: '10px' } },
                [
                  React.createElement(
                    'button',
                    {
                      key: 'button',
                      onClick: () => handleMenuClick(item.param),
                      style: {
                        width: '100%',
                        padding: '8px 12px',
                        border: 'none',
                        backgroundColor: currentPage === item.param ? '#007bff' : '#fff',
                        color: currentPage === item.param ? '#fff' : '#000',
                        cursor: 'pointer',
                        borderRadius: '4px',
                        textAlign: 'left'
                      }
                    },
                    item.title
                  ),
                  item.children && React.createElement(
                    'ul',
                    { 
                      key: 'submenu',
                      style: { 
                        listStyle: 'none', 
                        padding: '5px 0 0 15px',
                        margin: 0
                      } 
                    },
                    item.children.map((child, index) =>
                      React.createElement(
                        'li',
                        { key: index, style: { marginBottom: '5px' } },
                        React.createElement(
                          'button',
                          {
                            onClick: () => handleMenuClick(child.param, child.subParam),
                            style: {
                              width: '100%',
                              padding: '6px 10px',
                              border: 'none',
                              backgroundColor: 'transparent',
                              color: '#666',
                              cursor: 'pointer',
                              borderRadius: '4px',
                              textAlign: 'left',
                              fontSize: '14px'
                            }
                          },
                          child.title
                        )
                      )
                    )
                  )
                ]
              )
            )
          )
        ]
      ),
      
      // Main content area
      React.createElement(
        'div',
        { 
          key: 'content',
          style: { 
            flex: 1, 
            padding: '20px',
            backgroundColor: '#fff'
          } 
        },
        [
          React.createElement(
            'div',
            { key: 'header', style: { marginBottom: '20px' } },
            [
              React.createElement('h2', { key: 'title' }, `Current Page: ${currentPage}`),
              React.createElement('p', { key: 'url' }, `URL: ${location.pathname}${location.search}`)
            ]
          ),
          React.createElement(
            'div',
            { key: 'page-content' },
            renderPageContent()
          )
        ]
      )
    ]
  );
};

module.exports = ParamBasedNavigation;
