{"name": "management", "version": "0.0.0", "description": "This is the description of the plugin.", "strapi": {"name": "management", "description": "Description of Management plugin", "kind": "plugin", "displayName": "Management"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@fontsource/be-vietnam-pro": "^5.2.6", "@reduxjs/toolkit": "^1.9.7", "@strapi/design-system": "^1.6.3", "@strapi/helper-plugin": "^4.6.0", "@strapi/icons": "^1.6.3", "antd": "^5.26.6", "lucide-react": "^0.526.0", "prop-types": "^15.7.2", "react-router-dom": "^5.3.4", "recharts": "^3.1.0"}, "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "styled-components": "^5.3.6"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0", "react-router-dom": "^5.3.4", "styled-components": "^5.3.6"}, "author": {"name": "A Strapi developer"}, "maintainers": [{"name": "A Strapi developer"}], "engines": {"node": ">=16.0.0 <=20.x.x", "npm": ">=6.0.0"}, "license": "MIT"}