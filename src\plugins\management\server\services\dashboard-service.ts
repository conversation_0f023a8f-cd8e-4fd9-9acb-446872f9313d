import type { Core } from '@strapi/strapi';

const dashboardService = ({ strapi }: { strapi: Core.Strapi }) => ({
  async getDashboardData(query: any) {
    try {
      const kpiData = await this.getKPIData(query);
      const chartData = await this.getChartData('revenue', query);

      return {
        kpi: kpiData,
        charts: chartData,
      };
    } catch (error) {
      console.error('Error in getDashboardData:', error);
      throw error;
    }
  },

  async getKPIData(query: any) {
    try {
      // Get total orders and revenue
      const totalOrders = await strapi.entityService.count('api::don-hang.don-hang');

      const completedOrders = await strapi.entityService.findMany('api::don-hang.don-hang', {
        filters: {
          statusOrder: { $eq: 'Đã hoàn thành' },
        },
        fields: ['priceAfterTax'],
      });

      const ordersArray = Array.isArray(completedOrders) ? completedOrders : [];
      const totalRevenue = ordersArray.reduce((sum: number, order: any) => {
        return sum + (parseFloat(order.priceAfterTax) || 0);
      }, 0);

      // Get total users (agents)
      const totalUsers = await strapi.entityService.count('plugin::users-permissions.user');

      // Get total products
      const totalProducts = await strapi.entityService.count('api::san-pham.san-pham');

      return {
        totalRevenue,
        totalOrders,
        totalUsers,
        totalProducts,
      };
    } catch (error) {
      console.error('Error in getKPIData:', error);
      throw error;
    }
  },

  async getChartData(chartType: string, query: any) {
    try {
      if (chartType === 'revenue') {
        // Get revenue data for the last 12 months
        const currentDate = new Date();
        const monthsData = [];

        for (let i = 11; i >= 0; i--) {
          const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
          const nextDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - i + 1, 1);

          const monthName = date.toLocaleDateString('vi-VN', { month: 'short' });

          // Get orders for this month
          const monthOrders = await strapi.entityService.findMany('api::don-hang.don-hang', {
            filters: {
              createdAt: {
                $gte: date.toISOString(),
                $lt: nextDate.toISOString(),
              },
              statusOrder: { $eq: 'Đã hoàn thành' },
            },
            fields: ['priceAfterTax'],
          });

          const ordersArray = Array.isArray(monthOrders) ? monthOrders : [];
          const monthRevenue = ordersArray.reduce((sum: number, order: any) => {
            return sum + (parseFloat(order.priceAfterTax) || 0);
          }, 0);

          monthsData.push({
            month: monthName,
            revenue: monthRevenue,
            orders: ordersArray.length,
          });
        }

        return { data: monthsData };
      }

      return { data: [] };
    } catch (error) {
      console.error('Error in getChartData:', error);
      throw error;
    }
  },

  async getTopAffiliates(query: any) {
    try {
      // Get top customers by total spent
      const orders = await strapi.entityService.findMany('api::don-hang.don-hang', {
        filters: {
          statusOrder: { $eq: 'Đã hoàn thành' },
        },
        populate: {
          user: {
            fields: ['id', 'name', 'email'],
          },
        },
        fields: ['priceAfterTax'],
      });

      const ordersArray = Array.isArray(orders) ? orders : [];

      // Group by user and calculate totals
      const userStats = ordersArray.reduce((acc: any, order: any) => {
        if (order.user) {
          const userId = order.user.id;
          if (!acc[userId]) {
            acc[userId] = {
              id: userId,
              name: order.user.name,
              email: order.user.email,
              totalOrders: 0,
              totalSpent: 0,
            };
          }
          acc[userId].totalOrders += 1;
          acc[userId].totalSpent += parseFloat(order.priceAfterTax) || 0;
        }
        return acc;
      }, {});

      // Convert to array and sort by total spent
      const topCustomers = Object.values(userStats)
        .sort((a: any, b: any) => b.totalSpent - a.totalSpent)
        .slice(0, 5); // Top 5 customers

      return { data: topCustomers };
    } catch (error) {
      console.error('Error in getTopAffiliates:', error);
      throw error;
    }
  },

  async getBestSellingProducts(query: any) {
    try {
      // Get products sorted by da_ban (sold quantity)
      const products = await strapi.entityService.findMany('api::san-pham.san-pham', {
        sort: { da_ban: 'desc' },
        limit: 5,
        fields: ['name', 'da_ban', 'gia_ban'],
      });

      const productsArray = Array.isArray(products) ? products : [];

      const bestSelling = productsArray.map((product: any) => ({
        id: product.id,
        name: product.name,
        sold: product.da_ban || 0,
        revenue: (product.da_ban || 0) * (parseFloat(product.gia_ban) || 0),
      }));

      return { data: bestSelling };
    } catch (error) {
      console.error('Error in getBestSellingProducts:', error);
      throw error;
    }
  },

  getWelcomeMessage() {
    return 'Welcome to Management Plugin Dashboard!';
  },
});

export default dashboardService;
