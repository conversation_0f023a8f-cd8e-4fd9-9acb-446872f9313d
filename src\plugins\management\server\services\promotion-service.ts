import type { Core } from '@strapi/strapi';

const promotionService = ({ strapi }: { strapi: Core.Strapi }) => ({
  async listPromotions(query: any) {
    try {
      const { page = 1, pageSize = 10, status, search, type, dateRange } = query;

      // Build filters
      const filters: any = {};

      if (status) {
        if (status === 'active') {
          filters.isActive = true;
          filters.startDate = { $lte: new Date() };
          filters.endDate = { $gte: new Date() };
        } else if (status === 'inactive') {
          filters.isActive = false;
        } else if (status === 'expired') {
          filters.endDate = { $lt: new Date() };
        }
      }

      if (search) {
        filters.$or = [
          { name: { $containsi: search } },
          { code: { $containsi: search } },
          { description: { $containsi: search } },
        ];
      }

      if (type) {
        filters.type = type;
      }

      if (dateRange && dateRange.length === 2) {
        filters.createdAt = {
          $gte: new Date(dateRange[0]),
          $lte: new Date(dateRange[1]),
        };
      }

      const promotions = await strapi.entityService.findMany('api::khuyen-mai.khuyen-mai', {
        filters,
        populate: {
          applicableProducts: {
            fields: ['id', 'name'],
          },
          applicableCategories: {
            fields: ['id', 'name'],
          },
        },
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
        },
        sort: { createdAt: 'desc' },
      });

      // Get total count for pagination
      const total = await strapi.entityService.count('api::khuyen-mai.khuyen-mai', {
        filters,
      });

      return {
        success: true,
        data: {
          data: Array.isArray(promotions) ? promotions : [promotions],
          meta: {
            pagination: {
              total,
              page: Number(page),
              pageSize: Number(pageSize),
              pageCount: Math.ceil(total / Number(pageSize)),
            },
          },
        },
      };
    } catch (error: any) {
      throw new Error(`Failed to fetch promotions: ${error.message}`);
    }
  },

  async createPromotion(promotionData: any) {
    try {
      // Validate promotion code uniqueness
      const existingPromotion = await strapi.entityService.findMany('api::khuyen-mai.khuyen-mai', {
        filters: { code: promotionData.code },
      });

      if (existingPromotion && existingPromotion.length > 0) {
        throw new Error('Mã khuyến mãi đã tồn tại');
      }

      // Validate dates
      if (new Date(promotionData.startDate) >= new Date(promotionData.endDate)) {
        throw new Error('Ngày bắt đầu phải nhỏ hơn ngày kết thúc');
      }

      const promotion = await strapi.entityService.create('api::khuyen-mai.khuyen-mai', {
        data: {
          ...promotionData,
          usageCount: 0,
        } as any,
      });

      return {
        success: true,
        data: promotion,
        message: 'Tạo khuyến mãi thành công',
      };
    } catch (error: any) {
      throw new Error(`Failed to create promotion: ${error.message}`);
    }
  },

  async getPromotionDetail(id: number) {
    try {
      const promotion = await strapi.entityService.findOne('api::khuyen-mai.khuyen-mai', id, {
        populate: {
          applicableProducts: {
            fields: ['id', 'name', 'gia_ban'],
          },
          applicableCategories: {
            fields: ['id', 'name'],
          },
        },
      });

      if (!promotion) {
        throw new Error('Không tìm thấy khuyến mãi');
      }

      return {
        success: true,
        data: promotion,
      };
    } catch (error: any) {
      throw new Error(`Failed to fetch promotion detail: ${error.message}`);
    }
  },

  async updatePromotion(id: number, promotionData: any) {
    try {
      // Check if promotion exists
      const existingPromotion = await strapi.entityService.findOne(
        'api::khuyen-mai.khuyen-mai',
        id
      );
      if (!existingPromotion) {
        throw new Error('Không tìm thấy khuyến mãi');
      }

      // Validate promotion code uniqueness (if code is being changed)
      if (promotionData.code && promotionData.code !== existingPromotion.code) {
        const codeExists = await strapi.entityService.findMany('api::khuyen-mai.khuyen-mai', {
          filters: {
            code: promotionData.code,
            id: { $ne: id },
          },
        });

        if (codeExists && codeExists.length > 0) {
          throw new Error('Mã khuyến mãi đã tồn tại');
        }
      }

      // Validate dates
      if (promotionData.startDate && promotionData.endDate) {
        if (new Date(promotionData.startDate) >= new Date(promotionData.endDate)) {
          throw new Error('Ngày bắt đầu phải nhỏ hơn ngày kết thúc');
        }
      }

      const promotion = await strapi.entityService.update('api::khuyen-mai.khuyen-mai', id, {
        data: promotionData,
      });

      return {
        success: true,
        data: promotion,
        message: 'Cập nhật khuyến mãi thành công',
      };
    } catch (error: any) {
      throw new Error(`Failed to update promotion: ${error.message}`);
    }
  },

  async deletePromotion(id: number) {
    try {
      const promotion = await strapi.entityService.findOne('api::khuyen-mai.khuyen-mai', id);
      if (!promotion) {
        throw new Error('Không tìm thấy khuyến mãi');
      }

      await strapi.entityService.delete('api::khuyen-mai.khuyen-mai', id);

      return {
        success: true,
        message: 'Xóa khuyến mãi thành công',
      };
    } catch (error: any) {
      throw new Error(`Failed to delete promotion: ${error.message}`);
    }
  },

  async togglePromotionStatus(id: number, isActive: boolean) {
    try {
      const promotion = await strapi.entityService.update('api::khuyen-mai.khuyen-mai', id, {
        data: { isActive } as any,
      });

      return {
        success: true,
        data: promotion,
        message: `${isActive ? 'Kích hoạt' : 'Vô hiệu hóa'} khuyến mãi thành công`,
      };
    } catch (error: any) {
      throw new Error(`Failed to toggle promotion status: ${error.message}`);
    }
  },

  async getPromotionStatistics(query: any) {
    try {
      const now = new Date();

      // Get total promotions
      const totalPromotions = await strapi.entityService.count('api::khuyen-mai.khuyen-mai');

      // Get active promotions
      const activePromotions = await strapi.entityService.count('api::khuyen-mai.khuyen-mai', {
        filters: {
          isActive: true,
          startDate: { $lte: now },
          endDate: { $gte: now },
        },
      });

      // Get expired promotions
      const expiredPromotions = await strapi.entityService.count('api::khuyen-mai.khuyen-mai', {
        filters: {
          endDate: { $lt: now },
        },
      });

      // Get upcoming promotions
      const upcomingPromotions = await strapi.entityService.count('api::khuyen-mai.khuyen-mai', {
        filters: {
          startDate: { $gt: now },
        },
      });

      // Get total usage count
      const promotions = await strapi.entityService.findMany('api::khuyen-mai.khuyen-mai', {
        fields: ['usageCount'],
      });

      const totalUsage = promotions.reduce(
        (sum: number, promo: any) => sum + (promo.usageCount || 0),
        0
      );

      return {
        success: true,
        data: {
          totalPromotions,
          activePromotions,
          expiredPromotions,
          upcomingPromotions,
          totalUsage,
          totalDiscountAmount: 0, // This would need to be calculated from order data
        },
      };
    } catch (error: any) {
      throw new Error(`Failed to fetch promotion statistics: ${error.message}`);
    }
  },

  async validatePromotionCode(code: string, orderData: any) {
    try {
      const promotion = await strapi.entityService.findMany('api::khuyen-mai.khuyen-mai', {
        filters: { code },
        populate: {
          applicableProducts: true,
          applicableCategories: true,
        },
      });

      if (!promotion || promotion.length === 0) {
        return {
          success: false,
          message: 'Mã khuyến mãi không tồn tại',
        };
      }

      const promo = promotion[0];
      const now = new Date();

      // Check if promotion is active
      if (!promo.isActive) {
        return {
          success: false,
          message: 'Mã khuyến mãi đã bị vô hiệu hóa',
        };
      }

      // Check if promotion is within valid date range
      if (new Date(promo.startDate) > now) {
        return {
          success: false,
          message: 'Mã khuyến mãi chưa có hiệu lực',
        };
      }

      if (new Date(promo.endDate) < now) {
        return {
          success: false,
          message: 'Mã khuyến mãi đã hết hạn',
        };
      }

      // Check usage limit
      if (promo.usageLimit && promo.usageCount >= promo.usageLimit) {
        return {
          success: false,
          message: 'Mã khuyến mãi đã hết lượt sử dụng',
        };
      }

      // Check minimum order amount
      if (orderData.orderAmount < promo.minOrderAmount) {
        return {
          success: false,
          message: `Đơn hàng tối thiểu ${promo.minOrderAmount.toLocaleString('vi-VN')}đ để sử dụng mã này`,
        };
      }

      // Calculate discount amount
      let discountAmount = 0;
      if (promo.type === 'percentage') {
        discountAmount = (orderData.orderAmount * promo.value) / 100;
        if (promo.maxDiscountAmount && discountAmount > promo.maxDiscountAmount) {
          discountAmount = promo.maxDiscountAmount;
        }
      } else if (promo.type === 'fixed_amount') {
        discountAmount = promo.value;
      } else if (promo.type === 'free_shipping') {
        discountAmount = orderData.shippingAmount || 0;
      }

      return {
        success: true,
        data: {
          promotion: promo,
          discountAmount,
          finalAmount: orderData.orderAmount - discountAmount,
        },
        message: 'Mã khuyến mãi hợp lệ',
      };
    } catch (error: any) {
      throw new Error(`Failed to validate promotion code: ${error.message}`);
    }
  },

  async applyPromotion(code: string, orderData: any) {
    try {
      const validation = await this.validatePromotionCode(code, orderData);

      if (!validation.success) {
        return validation;
      }

      // Update usage count
      const promotion = validation.data.promotion;
      await strapi.entityService.update('api::khuyen-mai.khuyen-mai', promotion.id, {
        data: {
          usageCount: promotion.usageCount + 1,
        } as any,
      });

      return {
        success: true,
        data: validation.data,
        message: 'Áp dụng mã khuyến mãi thành công',
      };
    } catch (error: any) {
      throw new Error(`Failed to apply promotion: ${error.message}`);
    }
  },

  async getPromotionUsageHistory(id: number, query: any) {
    try {
      const { page = 1, pageSize = 10 } = query;

      // This would typically involve finding orders that used this promotion
      // For now, return empty data as we need to implement order-promotion relationship
      return {
        success: true,
        data: {
          data: [],
          meta: {
            pagination: {
              total: 0,
              page: Number(page),
              pageSize: Number(pageSize),
              pageCount: 0,
            },
          },
        },
      };
    } catch (error: any) {
      throw new Error(`Failed to fetch promotion usage history: ${error.message}`);
    }
  },

  async seedPromotions() {
    try {
      // Check if promotions already exist
      const existingPromotions = await strapi.entityService.findMany('api::khuyen-mai.khuyen-mai', {
        start: 0,
        limit: 1,
      });

      if (existingPromotions && existingPromotions.length > 0) {
        return {
          success: true,
          message: 'Dữ liệu khuyến mãi đã tồn tại',
        };
      }

      // Sample promotions data
      const samplePromotions = [
        {
          name: 'Giảm giá 10% cho đơn hàng đầu tiên',
          description: 'Khuyến mãi dành cho khách hàng mới, giảm 10% cho đơn hàng đầu tiên',
          code: 'FIRST10',
          type: 'percentage',
          value: 10,
          minOrderAmount: 100000,
          maxDiscountAmount: 50000,
          usageLimit: 100,
          usageCount: 0,
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          isActive: true,
          isPublic: true,
        },
        {
          name: 'Miễn phí vận chuyển',
          description: 'Miễn phí vận chuyển cho đơn hàng từ 200k',
          code: 'FREESHIP',
          type: 'free_shipping',
          value: 0,
          minOrderAmount: 200000,
          usageLimit: null,
          usageCount: 0,
          startDate: new Date(),
          endDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
          isActive: true,
          isPublic: true,
        },
        {
          name: 'Giảm 50k cho đơn từ 500k',
          description: 'Giảm 50.000đ cho đơn hàng từ 500.000đ',
          code: 'SAVE50K',
          type: 'fixed_amount',
          value: 50000,
          minOrderAmount: 500000,
          usageLimit: 50,
          usageCount: 0,
          startDate: new Date(),
          endDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
          isActive: true,
          isPublic: true,
        },
        {
          name: 'Khuyến mãi hết hạn',
          description: 'Khuyến mãi đã hết hạn để test',
          code: 'EXPIRED',
          type: 'percentage',
          value: 20,
          minOrderAmount: 100000,
          usageLimit: 10,
          usageCount: 0,
          startDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
          endDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
          isActive: false,
          isPublic: false,
        },
      ];

      // Create promotions
      const createdPromotions = [];
      for (const promotionData of samplePromotions) {
        const promotion = await strapi.entityService.create('api::khuyen-mai.khuyen-mai', {
          data: promotionData as any,
        });
        createdPromotions.push(promotion);
      }

      return {
        success: true,
        data: createdPromotions,
        message: `Đã tạo ${createdPromotions.length} khuyến mãi mẫu`,
      };
    } catch (error: any) {
      throw new Error(`Failed to seed promotions: ${error.message}`);
    }
  },
});

export default promotionService;
