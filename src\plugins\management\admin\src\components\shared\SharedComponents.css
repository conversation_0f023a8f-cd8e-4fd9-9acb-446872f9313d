/* Shared Components CSS for Management Plugin */

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Container Components */
.page-container {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
    'Helvetica Neue', sans-serif;
}

/* Card Components */
.card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.card-header {
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

.header-info {
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 20px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 4px 0;
}

.card-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-content {
  padding: 24px;
}

/* Button Components */
.btn {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  text-decoration: none;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn svg {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.btn-primary {
  background: #2563eb;
  color: #ffffff;
  border-color: #2563eb;
}

.btn-primary:hover:not(:disabled) {
  background: #1d4ed8;
  border-color: #1d4ed8;
}

.btn-success {
  background: #059669;
  color: #ffffff;
  border-color: #059669;
}

.btn-success:hover:not(:disabled) {
  background: #047857;
  border-color: #047857;
}

.btn-default {
  background: #ffffff;
  color: #374151;
  border-color: #d1d5db;
}

.btn-default:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* Search Components */
.filters-section {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }
}

.search-container {
  position: relative;
  flex: 1;
  max-width: 320px;
}

@media (max-width: 768px) {
  .search-container {
    max-width: none;
  }
}

.search-container svg {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 6px 12px 6px 36px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  background-color: #ffffff;
  outline: none;
  transition: border-color 0.2s ease;
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    'Roboto', sans-serif;
}

.search-input::placeholder {
  color: #9ca3af;
}

.search-input:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Filter Components */
.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  color: #6b7280;
  white-space: nowrap;
}

.date-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  background-color: #ffffff;
  outline: none;
  transition: border-color 0.2s ease;
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    'Roboto', sans-serif;
}

.date-input:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.select-input {
  padding: 6px 2px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  background-color: #ffffff;
  outline: none;
  transition: border-color 0.2s ease;
  font-family: 'Be Vietnam Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI',
    'Roboto', sans-serif;
}

.select-input:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Stats Components */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  position: relative;
  overflow: hidden;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.stat-content {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-title {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0 4px 0;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.stat-icon {
  padding: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon svg {
  width: 24px;
  height: 24px;
  color: white;
}

.stat-icon.bg-blue {
  background-color: #3b82f6;
}

.stat-icon.bg-blue-dark {
  background-color: #2563eb;
}

.stat-icon.bg-green {
  background-color: #10b981;
}

.stat-icon.bg-red {
  background-color: #ef4444;
}

/* Table Components */
.table-container {
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Loading Components */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  background: #ffffff;
  border-radius: 8px;
}

/* Empty State Components */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: #ffffff;
  border-radius: 8px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  color: #9ca3af;
}

.empty-icon svg {
  width: 48px;
  height: 48px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* Action Button Components */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-button {
  width: 32px;
  height: 32px;
  padding: 0;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-button svg {
  width: 16px;
  height: 16px;
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-button:disabled:hover {
  background: #ffffff !important;
  border-color: #d1d5db !important;
}

.action-button.view {
  color: #2563eb;
}

.action-button.view:hover:not(:disabled) {
  background: #eff6ff;
  border-color: #2563eb;
}

.action-button.edit {
  color: #10b981;
}

.action-button.edit:hover:not(:disabled) {
  background: #ecfdf5;
  border-color: #10b981;
}

.action-button.delete {
  color: #ef4444;
}

.action-button.delete:hover:not(:disabled) {
  background: #fef2f2;
  border-color: #ef4444;
}

/* Image Components */
.image-container {
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.image-container .ant-image {
  width: 100%;
  height: 100%;
}

.image-container .ant-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.placeholder-container {
  background-color: #f5f5f5;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 12px;
  text-align: center;
  flex-shrink: 0;
}

/* File Upload Components */
.file-upload-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.file-list-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-family: 'Be Vietnam Pro', sans-serif;
}

.file-item:hover {
  background-color: #f0f0f0;
  border-color: #667eea;
}

/* Upload Container */
.upload-container {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  flex-wrap: wrap;
}

/* Image Preview Container */
.image-preview-container {
  position: relative;
  width: 102px;
  height: 102px;
  margin-inline-end: 8px;
  margin-bottom: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Remove Button */
.remove-button:hover {
  background: rgba(0, 0, 0, 0.7) !important;
}

/* Upload Button */
.upload-button:hover {
  border-color: #1890ff !important;
}

/* Styled Table */
.styled-table .ant-table .ant-table-thead > tr > th {
  padding: 12px;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.styled-table .ant-table .ant-table-tbody > tr > td {
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
  color: #374151;
}

.styled-table .ant-table .ant-table-tbody > tr:hover > td {
  background: #f9fafb;
}

.styled-table .ant-table .ant-table-pagination {
  margin: 16px 0 0 0;
}

.styled-table .ant-table .ant-table-pagination .ant-pagination-item {
  border-radius: 6px;
}

.styled-table .ant-table .ant-table-pagination .ant-pagination-item-active {
  background: #2563eb;
  border-color: #2563eb;
}

.styled-table .ant-table .ant-table-pagination .ant-pagination-item-active a {
  color: #ffffff;
}
