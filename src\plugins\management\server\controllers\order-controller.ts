import type { Core } from '@strapi/strapi';

const orderController = ({ strapi }: { strapi: Core.Strapi }) => ({
  async listOrders(ctx) {
    ctx.body = await strapi.plugin('management').service('order-service').listOrders(ctx.query);
  },

  async createOrder(ctx) {
    const { orderData } = ctx.request.body;
    ctx.body = await strapi.plugin('management').service('order-service').createOrder(orderData);
  },

  async updateOrderStatus(ctx) {
    const { id } = ctx.params;
    const { status } = ctx.request.body;

    // Validate ID parameter
    const orderId = parseInt(id, 10);
    if (isNaN(orderId) || orderId <= 0) {
      ctx.throw(400, 'Invalid order ID');
      return;
    }

    ctx.body = await strapi
      .plugin('management')
      .service('order-service')
      .updateOrderStatus(orderId, status);
  },

  async updateOrderCustomer(ctx) {
    const { id } = ctx.params;
    const { customer } = ctx.request.body;

    // Validate ID parameter
    const orderId = parseInt(id, 10);
    if (isNaN(orderId) || orderId <= 0) {
      ctx.throw(400, 'Invalid order ID');
      return;
    }

    ctx.body = await strapi
      .plugin('management')
      .service('order-service')
      .updateOrderCustomer(orderId, customer);
  },

  async exportOrdersToExcel(ctx) {
    try {
      // Get query parameters from either query string (GET) or request body (POST)
      const queryParams = ctx.method === 'POST' ? ctx.request.body : ctx.query;

      const buffer = await strapi
        .plugin('management')
        .service('order-service')
        .exportOrdersToExcel(queryParams);

      // Generate filename with current date
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format
      const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-'); // HH-MM-SS format
      const filename = `don-hang-${dateStr}-${timeStr}.xlsx`;

      ctx.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      ctx.set('Content-Disposition', `attachment; filename="${filename}"`);
      ctx.body = buffer;
    } catch (error) {
      console.error('Export error:', error);
      ctx.badRequest('Export failed');
    }
  },

  async getOrderDetail(ctx) {
    const { id } = ctx.params;

    // Validate ID parameter
    const orderId = parseInt(id, 10);
    if (isNaN(orderId) || orderId <= 0) {
      ctx.throw(400, 'Invalid order ID');
      return;
    }

    ctx.body = await strapi.plugin('management').service('order-service').getOrderDetail(orderId);
  },

  async updateCommission(ctx) {
    const { id } = ctx.params;
    const { commissionData } = ctx.request.body;

    // Validate ID parameter
    const orderId = parseInt(id, 10);
    if (isNaN(orderId) || orderId <= 0) {
      ctx.throw(400, 'Invalid order ID');
      return;
    }

    ctx.body = await strapi
      .plugin('management')
      .service('order-service')
      .updateCommission(orderId, commissionData);
  },

  async getOrderStatistics(ctx) {
    ctx.body = await strapi
      .plugin('management')
      .service('order-service')
      .getOrderStatistics(ctx.query);
  },
});

export default orderController;
