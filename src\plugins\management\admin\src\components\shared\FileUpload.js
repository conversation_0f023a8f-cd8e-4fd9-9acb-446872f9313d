const React = require('react');
const { Upload, Button, message, Space } = require('antd');
const {
  UploadOutlined,
  FileOutlined,
  DeleteOutlined,
} = require('@ant-design/icons');

// Import CSS
require('./SharedComponents.css');

// All styles are now in SharedComponents.css

const FileUpload = ({
  value = [],
  onChange,
  accept = '*',
  maxCount = 1,
  disabled = false,
  buttonText = 'Tải lên',
  beforeUpload,
  showFileList = true,
}) => {
  const handleUploadChange = (info) => {
    let newFileList = [...info.fileList];

    // Ensure each file has the correct status and uid
    newFileList = newFileList.map((file) => {
      if (!file.uid) {
        file.uid = `upload-${Date.now()}-${Math.random()}`;
      }
      if (!file.status) {
        file.status = 'done';
      }
      return file;
    });

    // Limit the number of files
    if (maxCount && newFileList.length > maxCount) {
      newFileList = newFileList.slice(-maxCount);
    }

    onChange?.(newFileList);
  };

  const handleRemove = (fileToRemove) => {
    const newFileList = value.filter((file) => file.uid !== fileToRemove.uid);
    onChange?.(newFileList);
  };

  const defaultBeforeUpload = (file) => {
    // Default validation can be customized
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('File phải nhỏ hơn 10MB!');
      return false;
    }

    return false; // Prevent auto upload
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const showUploadButton = !maxCount || value.length < maxCount;

  return React.createElement(
    'div',
    { className: 'file-upload-container' },
    // Display uploaded files
    showFileList &&
      value.length > 0 &&
      React.createElement(
        'div',
        { className: 'file-list-container' },
        value.map((file) =>
          React.createElement(
            'div',
            { key: file.uid, className: 'file-item' },
            React.createElement(
              Space,
              { align: 'center' },
              React.createElement(FileOutlined, {
                style: { color: '#1890ff', fontSize: 16 },
              }),
              React.createElement(
                'div',
                null,
                React.createElement(
                  'div',
                  {
                    style: { fontSize: 14, color: '#262626', fontWeight: 500 },
                  },
                  file.name
                ),
                file.size &&
                  React.createElement(
                    'div',
                    { style: { fontSize: 12, color: '#8c8c8c' } },
                    formatFileSize(file.size)
                  )
              )
            ),
            !disabled &&
              React.createElement(Button, {
                type: 'text',
                size: 'small',
                icon: React.createElement(DeleteOutlined),
                onClick: () => handleRemove(file),
                danger: true,
              })
          )
        )
      ),
    // Upload button
    showUploadButton &&
      !disabled &&
      React.createElement(
        Upload,
        {
          fileList: value,
          onChange: handleUploadChange,
          beforeUpload: beforeUpload || defaultBeforeUpload,
          accept: accept,
          maxCount: maxCount,
          showUploadList: false,
          disabled: disabled,
        },
        React.createElement(
          Button,
          {
            icon: React.createElement(UploadOutlined),
            disabled: disabled,
            block: true,
            style: {
              height: 40,
              borderRadius: 8,
              border: '1px dashed #d9d9d9',
              backgroundColor: '#fafafa',
              fontFamily: 'Be Vietnam Pro, sans-serif',
              fontWeight: 500,
            },
          },
          buttonText
        )
      )
  );
};

module.exports = FileUpload;
