﻿const React = require('react');
const { Typography, Card } = require('antd');

const { Title } = Typography;

const Settings = () => {
  return React.createElement(
    Card,
    {
      style: {
        margin: '24px',
        minHeight: '400px',
      },
    },
    [
      React.createElement(
        Title,
        {
          key: 'title',
          level: 2,
          style: {
            textAlign: 'center',
            color: '#6b7280',
            marginTop: '100px',
          },
        },
        'Cài đặt'
      ),
      React.createElement(
        'p',
        {
          key: 'description',
          style: {
            textAlign: 'center',
            color: '#9ca3af',
            fontSize: '16px',
          },
        },
        'Tính năng đang được phát triển...'
      ),
    ]
  );
};

module.exports = Settings;
