import type { Core } from '@strapi/strapi';

const dashboardController = ({ strapi }: { strapi: Core.Strapi }) => ({
  async getDashboardData(ctx) {
    ctx.body = await strapi.plugin('management').service('dashboard-service').getDashboardData(ctx.query);
  },

  async getKPIData(ctx) {
    ctx.body = await strapi.plugin('management').service('dashboard-service').getKPIData(ctx.query);
  },

  async getChartData(ctx) {
    const { chartType } = ctx.params;
    ctx.body = await strapi.plugin('management').service('dashboard-service').getChartData(chartType, ctx.query);
  },

  async getTopAffiliates(ctx) {
    ctx.body = await strapi.plugin('management').service('dashboard-service').getTopAffiliates(ctx.query);
  },

  async getBestSellingProducts(ctx) {
    ctx.body = await strapi.plugin('management').service('dashboard-service').getBestSellingProducts(ctx.query);
  },

  index(ctx) {
    ctx.body = strapi.plugin('management').service('dashboard-service').getWelcomeMessage();
  },
});

export default dashboardController;
