// Import all styled components (CSS version)
const StyledComponents = require('./StyledComponents');

// Import shared components
const SearchBar = require('./SearchBar');
const PageHeader = require('./PageHeader');
const StatsCard = require('./StatsCard');
const ActionButtonGroup = require('./ActionButtonGroup');
const CategoryBrandModal = require('./CategoryBrandModal');
const QuickAddModal = require('./QuickAddModal');
const ImageDisplay = require('./ImageDisplay');
const SharedImageUpload = require('./SharedImageUpload');
const FileUpload = require('./FileUploadPlain');

// Export all styled components
module.exports = {
  ...StyledComponents,
  SearchBar,
  PageHeader,
  StatsCard,
  ActionButtonGroup,
  CategoryBrandModal,
  QuickAddModal,
  ImageDisplay,
  SharedImageUpload,
  FileUpload,
};
