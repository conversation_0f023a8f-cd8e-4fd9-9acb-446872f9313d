const React = require('react');
const { useState, useEffect, useRef } = React;
const { Search, Bell, User, Package, Clock } = require('lucide-react');
const { Badge, Spin, Empty } = require('antd');
const { useFetchClient } = require('@strapi/helper-plugin');
const { useHistory } = require('react-router-dom');

const DashboardHeader = ({ collapsed = false }) => {
  const { get } = useFetchClient();
  const history = useHistory();
  const [notifications, setNotifications] = useState([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dropdownVisible, setDropdownVisible] = useState(false);

  // Styles
  const styles = {
    header: {
      background: '#ffffff',
      padding: '16px 24px',
      position: 'fixed',
      top: 0,
      right: 0,
      left: collapsed ? '70px' : '256px',
      zIndex: 999,
      transition: 'left 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    },
    headerContent: {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    leftSection: {
      display: 'flex',
      alignItems: 'center',
    },
    rightSection: {
      display: 'flex',
      alignItems: 'center',
      gap: '16px',
    },
    searchContainer: {
      position: 'relative',
    },
    searchIcon: {
      width: '16px',
      height: '16px',
      position: 'absolute',
      left: '12px',
      top: '50%',
      transform: 'translateY(-50%)',
      color: '#9ca3af',
    },
    searchInput: {
      padding: '8px 16px 8px 40px',
      border: '1px solid #d1d5db',
      borderRadius: '8px',
      fontSize: '14px',
      fontFamily: "'Be Vietnam Pro', sans-serif",
      outline: 'none',
      transition: 'all 0.2s ease',
      width: '240px',
    },
    notificationButton: {
      position: 'relative',
      padding: '8px',
      background: 'transparent',
      border: 'none',
      cursor: 'pointer',
      borderRadius: '8px',
      transition: 'background-color 0.2s ease',
    },
    bellIcon: {
      width: '20px',
      height: '20px',
      color: '#6b7280',
    },
    notificationContainer: {
      position: 'relative',
    },
    notificationDropdown: {
      position: 'absolute',
      top: '100%',
      right: 0,
      width: '320px',
      maxHeight: '400px',
      background: 'white',
      borderRadius: '8px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      overflow: 'hidden',
      zIndex: 1000,
      marginTop: '8px',
      display: dropdownVisible ? 'block' : 'none',
    },
    notificationHeader: {
      padding: '16px',
      borderBottom: '1px solid #f0f0f0',
      background: '#fafafa',
    },
    notificationTitle: {
      margin: 0,
      fontSize: '14px',
      fontWeight: 600,
      color: '#262626',
      fontFamily: "'Be Vietnam Pro', sans-serif",
    },
    notificationList: {
      maxHeight: '300px',
      overflowY: 'auto',
    },
    notificationItem: {
      padding: '12px 16px',
      borderBottom: '1px solid #f0f0f0',
      cursor: 'pointer',
      transition: 'background-color 0.2s ease',
    },
    notificationContent: {
      display: 'flex',
      alignItems: 'flex-start',
      gap: '12px',
    },
    notificationIcon: {
      width: '32px',
      height: '32px',
      borderRadius: '6px',
      background: '#e6f7ff',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      flexShrink: 0,
    },
    notificationText: {
      flex: 1,
    },
    notificationMessage: {
      fontSize: '13px',
      color: '#262626',
      fontFamily: "'Be Vietnam Pro', sans-serif",
      marginBottom: '4px',
    },
    notificationTime: {
      fontSize: '12px',
      color: '#8c8c8c',
      fontFamily: "'Be Vietnam Pro', sans-serif",
    },
    notificationEmpty: {
      padding: '40px 16px',
      textAlign: 'center',
      color: '#8c8c8c',
      fontFamily: "'Be Vietnam Pro', sans-serif",
    },
    userSection: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
    },
    userAvatar: {
      width: '32px',
      height: '32px',
      backgroundColor: '#d1d5db',
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
    userIcon: {
      width: '16px',
      height: '16px',
      color: '#6b7280',
    },
    userName: {
      fontSize: '14px',
      color: '#374151',
      fontFamily: "'Be Vietnam Pro', sans-serif",
      fontWeight: 500,
    },
    loadingContainer: {
      padding: '20px',
      textAlign: 'center',
    },
  };

  // Fetch notifications
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      // Fetch pending orders
      const { data: ordersData } = await get('/management/orders', {
        params: { status: 'Chờ xác nhận', pageSize: 10 },
      });

      const pendingOrders = ordersData?.data || [];

      // Create notifications from pending orders
      const orderNotifications = pendingOrders.map((order) => ({
        id: `order-${order.id}`,
        type: 'order',
        title: 'Đơn hàng mới',
        message: `Đơn hàng ${order.code} cần được xác nhận`,
        time: formatTimeAgo(order.createdAt),
        data: order,
      }));

      setNotifications(orderNotifications);
      setNotificationCount(orderNotifications.length);
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // Format time ago
  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) return 'Vừa xong';
    if (diffInMinutes < 60) return `${diffInMinutes} phút trước`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} giờ trước`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} ngày trước`;
  };

  // Handle notification click
  const handleNotificationClick = (notification) => {
    if (notification.type === 'order') {
      history.push(`orders`);
    }
    setDropdownVisible(false);
  };

  // Fetch notifications on component mount and set up polling
  useEffect(() => {
    fetchNotifications();

    // Poll for new notifications every 30 seconds
    const interval = setInterval(fetchNotifications, 30000);

    return () => clearInterval(interval);
  }, []);

  // Handle click outside to close dropdown
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownVisible(false);
      }
    };

    if (dropdownVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownVisible]);

  return React.createElement(
    'div',
    { style: styles.header },
    React.createElement('div', { style: styles.headerContent }, [
      React.createElement('div', { key: 'left', style: styles.leftSection }),

      React.createElement('div', { key: 'right', style: styles.rightSection }, [
        React.createElement(
          'div',
          { key: 'search', style: styles.searchContainer },
          [
            React.createElement(Search, {
              key: 'search-icon',
              style: styles.searchIcon,
            }),
            React.createElement('input', {
              key: 'search-input',
              style: styles.searchInput,
              type: 'text',
              placeholder: 'Search...',
            }),
          ]
        ),

        React.createElement(
          'div',
          {
            key: 'notifications',
            style: styles.notificationContainer,
            ref: dropdownRef,
          },
          [
            React.createElement(
              'button',
              {
                key: 'notification-button',
                style: styles.notificationButton,
                onClick: () => setDropdownVisible(!dropdownVisible),
              },
              React.createElement(
                Badge,
                {
                  count: notificationCount,
                  size: 'small',
                  offset: [-2, 2],
                },
                React.createElement(Bell, { style: styles.bellIcon })
              )
            ),

            React.createElement(
              'div',
              {
                key: 'dropdown',
                style: styles.notificationDropdown,
              },
              [
                React.createElement(
                  'div',
                  { key: 'header', style: styles.notificationHeader },
                  React.createElement(
                    'h4',
                    { style: styles.notificationTitle },
                    `Thông báo (${notificationCount})`
                  )
                ),

                loading
                  ? React.createElement(
                      'div',
                      {
                        key: 'loading',
                        style: styles.loadingContainer,
                      },
                      React.createElement(Spin, { size: 'small' })
                    )
                  : notifications.length > 0
                  ? React.createElement(
                      'div',
                      { key: 'list', style: styles.notificationList },
                      notifications.map((notification) =>
                        React.createElement(
                          'div',
                          {
                            key: notification.id,
                            style: styles.notificationItem,
                            onClick: () =>
                              handleNotificationClick(notification),
                          },
                          React.createElement(
                            'div',
                            { style: styles.notificationContent },
                            [
                              React.createElement(
                                'div',
                                { key: 'icon', style: styles.notificationIcon },
                                notification.type === 'order' &&
                                  React.createElement(Package, {
                                    size: 16,
                                    color: '#1890ff',
                                  }),
                                notification.type === 'user' &&
                                  React.createElement(User, {
                                    size: 16,
                                    color: '#52c41a',
                                  }),
                                notification.type === 'withdrawal' &&
                                  React.createElement(Clock, {
                                    size: 16,
                                    color: '#faad14',
                                  })
                              ),
                              React.createElement(
                                'div',
                                { key: 'text', style: styles.notificationText },
                                [
                                  React.createElement(
                                    'div',
                                    {
                                      key: 'message',
                                      style: styles.notificationMessage,
                                    },
                                    notification.message
                                  ),
                                  React.createElement(
                                    'div',
                                    {
                                      key: 'time',
                                      style: styles.notificationTime,
                                    },
                                    notification.time
                                  ),
                                ]
                              ),
                            ]
                          )
                        )
                      )
                    )
                  : React.createElement(
                      'div',
                      { key: 'empty', style: styles.notificationEmpty },
                      React.createElement(Empty, {
                        image: Empty.PRESENTED_IMAGE_SIMPLE,
                        description: 'Không có thông báo mới',
                      })
                    ),
              ]
            ),
          ]
        ),

        React.createElement('div', { key: 'user', style: styles.userSection }, [
          React.createElement(
            'div',
            { key: 'avatar', style: styles.userAvatar },
            React.createElement(User, { style: styles.userIcon })
          ),
          React.createElement(
            'span',
            { key: 'name', style: styles.userName },
            'Admin'
          ),
        ]),
      ]),
    ])
  );
};

module.exports = DashboardHeader;
